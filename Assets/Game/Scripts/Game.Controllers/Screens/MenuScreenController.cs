using System;
using System.Reactive.Linq;
using System.Threading;
using Cysharp.Threading.Tasks;
using Cysharp.Threading.Tasks.Linq;
using Game.Core;
using Game.Models;
using Game.Views.Players;
using Game.Views.UI.Screens.Menu;
using Modules.Core;
using Modules.Network;
using Modules.UI;
using Modules.XR;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.InputSystem;
using UnityEngine.XR.Interaction.Toolkit.UI;
using VContainer;

namespace Game.Controllers.Screens
{
    public class MenuScreenController : ControllerBase
    {
        private const float MaxSqrDistanceBetweenScreenToPlayer = 10;
        private const float ActivationScreenDuration = 20;

        private GameModel gameModel;
        private IXRPlayer xrPlayer;
        private IXRInput xrInput;
        private IXRInteractors xrInteractors;
        private GameConfig gameConfig;
        private MenuScreen menuScreen;
        private XRUIInputModule xrUIInputModule;
        private INetworkClient networkClient;
        private float deactivationScreenTime;
        private bool isInitialized;
        private CancellationTokenSource processActiveScreenCancellationTokenSource;
        private CancellationTokenSource menuScreenCancellationTokenSource;

        private bool IsAvailable => networkClient.IsConnected.Value && gameModel.IsInitialized.Value;

        [Inject]
        private void Construct(
            IXRSystem xrSystem,
            IXRInput xrInput,
            IXRPlayer xrPlayer,
            IXRInteractors xrInteractors,
            IScreenManager screenManager,
            GameConfig gameConfig,
            INetworkClient networkClient,
            GameModel gameModel,
            PlayersModel playersModel)
        {
            this.gameModel = gameModel;
            this.networkClient = networkClient;
            this.xrInput = xrInput;
            this.xrPlayer = xrPlayer;
            this.xrInteractors = xrInteractors;
            this.gameConfig = gameConfig;
            xrUIInputModule = xrSystem.XRUIInputModule;
            menuScreen = screenManager.GetScreen<MenuScreen>(true);

            networkClient.IsConnected.Subscribe(_ => HandleInitializeMenuScreen()).AddTo(DisposeCancellationToken);
            gameModel.IsInitialized.Subscribe(_ => HandleInitializeMenuScreen()).AddTo(DisposeCancellationToken);
            playersModel.LocalPlayer.Where(p => p).Subscribe(p => p.Scale.Subscribe(menuScreen.SetScale).AddTo(p)).AddTo(DisposeCancellationToken);
        }

        public override void Dispose()
        {
            base.Dispose();
            xrUIInputModule.pointerUp -= HandleAnyUIInputClick;
            menuScreenCancellationTokenSource.CancelAndDispose();
            processActiveScreenCancellationTokenSource.CancelAndDispose();
        }

        private void HandleInitializeMenuScreen()
        {
            menuScreenCancellationTokenSource.CancelAndDispose();

            if (IsAvailable && !isInitialized)
            {
                menuScreenCancellationTokenSource = new CancellationTokenSource();
                menuScreen.IsActive.Subscribe(HandleMenuScreenActivation).AddTo(menuScreenCancellationTokenSource.Token);
                menuScreen.OnGrabStateUpdated.Where(args => !args.isGrabbed).Subscribe(_ => HandleUnGrab()).AddTo(menuScreenCancellationTokenSource.Token);
                xrInput.OnMenuButtonClick.Subscribe(HandleActivationMenuScreen).AddTo(menuScreenCancellationTokenSource.Token);
                isInitialized = true;
            }
            else if (!IsAvailable && isInitialized)
            {
                menuScreen.Hide();
                isInitialized = false;
            }
        }

        private void HandleMenuScreenActivation(bool isActive)
        {
            if (isActive)
            {
                var scale = menuScreen.transform.localScale.x;
                var headNode = xrPlayer.HeadNode;
                var position = headNode.position + 0.5f * scale * headNode.forward.SetY(0) + 0.25f * scale * Vector3.down;
                var rotation = Quaternion.LookRotation(headNode.forward.SetY(0)) * Quaternion.Euler(45, 0, 0);
                menuScreen.SetPositionAndRotation(position, rotation);

                xrInteractors.ApplyRayConfig(gameConfig.DefaultRayInteractorConfig);

                StartProcessDeactivationScreen();
            }
            else
            {
                xrInteractors.ApplyRayConfig(gameConfig.DisabledRayInteractorConfig);

                processActiveScreenCancellationTokenSource.CancelAndDispose();
            }
        }

        private void StartProcessDeactivationScreen()
        {
            UpdateDeactivationScreenTime();

            xrUIInputModule.pointerUp -= HandleAnyUIInputClick;
            xrUIInputModule.pointerUp += HandleAnyUIInputClick;

            processActiveScreenCancellationTokenSource.CancelAndDispose();
            processActiveScreenCancellationTokenSource = new CancellationTokenSource();
            UniTaskAsyncEnumerable.Interval(TimeSpan.FromSeconds(1)).ForEachAsync(_ =>
            {
                DisableScreenByDistance();
                DisableScreenByTimer();
            }, processActiveScreenCancellationTokenSource.Token);
        }

        private void HandleActivationMenuScreen(InputAction.CallbackContext ctx)
        {
            if (!ctx.canceled)
            {
                return;
            }

            if (menuScreen.IsActive.Value)
            {
                menuScreen.Hide();
            }
            else
            {
                menuScreen.OpenPanel<MenuPanel>();
                menuScreen.Show();
            }
        }

        private void HandleUnGrab()
        {
            UpdateDeactivationScreenTime();
        }

        private void HandleAnyUIInputClick(GameObject uiObject, PointerEventData eventData)
        {
            if (uiObject == null || uiObject.GetComponentInParent<MenuScreen>() == null)
            {
                return;
            }

            UpdateDeactivationScreenTime();
        }

        private void UpdateDeactivationScreenTime()
        {
            deactivationScreenTime = Time.time + ActivationScreenDuration;
        }

        private void DisableScreenByDistance()
        {
            var sqrRange = (menuScreen.transform.position - xrPlayer.HeadNode.position).sqrMagnitude;

            if (sqrRange > MaxSqrDistanceBetweenScreenToPlayer)
            {
                menuScreen.Hide();
            }
        }

        private void DisableScreenByTimer()
        {
            if (deactivationScreenTime < Time.time && !menuScreen.IsGrabbed)
            {
                menuScreen.Hide();
            }
        }
    }
}