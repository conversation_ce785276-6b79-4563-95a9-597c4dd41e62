using System;
using System.Reactive;
using System.Reactive.Subjects;
using System.Threading;
using Cysharp.Threading.Tasks;
using Modules.Core;
using Modules.UI;
using UnityEngine;

namespace Game.Views.UI.Screens.Menu
{
    public class SettingsPanel : MenuScreenPanelBase
    {
        [SerializeField] private ButtonWidget scaleUpWidget;
        [SerializeField] private ButtonWidget scaleDownWidget;

        private readonly ISubject<Unit> onScaleUp = new Subject<Unit>();
        private readonly ISubject<Unit> onScaleDown = new Subject<Unit>();
        
        private CancellationTokenSource disableCancellationTokenSource;

        public IObservable<Unit> OnScaleUp => onScaleUp;
        public IObservable<Unit> OnScaleDown => onScaleDown;

        private void OnEnable()
        {
            disableCancellationTokenSource.CancelAndDispose();
            disableCancellationTokenSource = new CancellationTokenSource();

            scaleUpWidget.OnClicked.Subscribe(_ => {
                onScaleUp.OnNext(Unit.Default);
            }).AddTo(disableCancellationTokenSource.Token);

            scaleDownWidget.OnClicked.Subscribe(_ => {
                onScaleDown.OnNext(Unit.Default);
            }).AddTo(disableCancellationTokenSource.Token);
        }
        
        private void OnDisable()
        {
            disableCancellationTokenSource.CancelAndDispose();
        }

        public void SetActiveScaleUpButton(bool isActive)
        {
            scaleUpWidget.SetActive(isActive);
        }

        public void SetActiveScaleDownButton(bool isActive)
        {
            scaleDownWidget.SetActive(isActive);
        }
    }
}
